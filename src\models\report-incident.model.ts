import { Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import { LocationTwo } from './location-two.model';
import { LocationThree } from './location-three.model';
import { LocationSix } from './location-six.model';
import { LocationOne } from './location-one.model';
import { LocationFour } from './location-four.model';
import { LocationFive } from './location-five.model';
import { Lighting } from './lighting.model';
import { SurfaceType } from './surface-type.model';
import { SurfaceCondition } from './surface-condition.model';
import { RiskCategory } from './risk-category.model';
import { IncidentUnderlyingCause } from './incident-underlying-cause.model';
import { IncidentUnderlyingCauseType } from './incident-underlying-cause-type.model';
import { IncidentUnderlyingCauseDescription } from './incident-underlying-cause-description.model';
import { IncidentRootCauseType } from './incident-root-cause-type.model';
import { IncidentRootCauseDescription } from './incident-root-cause-description.model';
import { WeatherCondition } from './weather-condition.model';
import { WorkActivity } from './work-activity.model';
import { IncidentCircumstanceCategory } from './incident-circumstance-category.model';
import { IncidentCircumstanceDescription } from './incident-circumstance-description.model';
import { IncidentCircumstanceType } from './incident-circumstance-type.model';
import { User } from './user.model';
import {Action} from './action.model';

@model()
export class ReportIncident extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',

  })
  IncidentCategory?: string;

  @property({
    type: 'string',

  })
  dangerousOccurance?: string;

  @property({
    type: 'string',

  })
  fatality?: string;

  @property({
    type: 'string',

  })
  title?: string;

  @property({
    type: 'string',

  })
  injury?: string;

  @property({
    type: 'string',

  })
  lostTime?: string;

  @property({
    type: 'string',

  })
  medicalTreatment?: string;

  @property({
    type: 'string',

  })
  firstAid?: string;

  @property({
    type: 'string',

  })
  lossOfConscious?: string;

  @property({
    type: 'string',

  })
  actualImpact?: string;

  @property({
    type: 'string',

  })
  potentialImpact?: string;

  @property({
    type: 'string',

  })
  reportAuthority?: string;

  @property({
    type: 'string',

  })
  authorityName?: string;

  @property({
    type: 'date',

  })
  date?: string;


  @property({
    type: 'string',

  })
  propertyDamage?: string;

  @property({
    type: 'string',

  })
  propertyDamageDetails?: string;

  @property({
    type: 'string',

  })
  maskId?: string;

  @property({
    type: 'string',

  })
  stopWorkOrder?: string;

  @property({
    type: 'boolean',

  })
  isWorkRelated?: boolean;

  

  @property({
    type: 'string',

  })
  workRelatedDetails?: string;

  @property({
    type: 'any',

  })
  incidentData?: any;

  @property({
    type: 'boolean',

  })
  isInjury?: boolean;

  @property({
    type: 'boolean',

  })
  isFirstAid?: boolean;

  @property({
    type: 'boolean',

  })
  isPersonInjured?: boolean;

  @property({
    type: 'boolean',

  })
  isMedicalTreatment?: boolean;

  @property({
    type: 'boolean',

  })
  isControlMeasure?: boolean;

  @property({
    type: 'boolean',

  })
  isRiskAssessment?: boolean;

  @property({
    type: 'boolean',

  })
  isMedicalLeave?: boolean;

  @property({
    type: 'string',

  })
  created?: string;

  @property({
    type: 'string',

  })
  incidentDate?: string;

  @property({
    type: 'array',
    itemType: 'string',

  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'string',

  })
  evidence?: string[];

  @property({
    type: 'array',
    itemType: 'string',

  })
  additionalDocuments?: string[];

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  classification?: string;

  @property({
    type: 'string',
  })
  incidentOwner?: string;

  @property({
    type: 'string',
  })
  investigationRemarks?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'any',

  })
  informationStep?: any;

  @property({
    type: 'any',

  })
  investigationStep?: any;

  @property({
    type: 'boolean',

  })
  investigationStatus?: boolean;

  @property({
    type: 'boolean',

  })
  triggerInvestigationStatus?: boolean;

  @property({
    type: 'any',

  })
  riskControl?: any;

  @property({
    type: 'boolean',

  })
  active?: boolean;


  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => Lighting)
  lightingId: string;

  @belongsTo(() => SurfaceType)
  surfaceTypeId: string;

  @belongsTo(() => SurfaceCondition)
  surfaceConditionId: string;

  @belongsTo(() => RiskCategory)
  riskCategoryId: string;

  @belongsTo(() => IncidentUnderlyingCause)
  incidentUnderlyingCauseId: string;

  @belongsTo(() => IncidentUnderlyingCauseType)
  incidentUnderlyingCauseTypeId: string;

  @belongsTo(() => IncidentUnderlyingCauseDescription)
  incidentUnderlyingCauseDescriptionId: string;

  @belongsTo(() => IncidentRootCauseType)
  incidentRootCauseTypeId: string;

  @belongsTo(() => IncidentRootCauseDescription)
  incidentRootCauseDescriptionId: string;

  @belongsTo(() => WeatherCondition)
  weatherConditionId: string;

  @belongsTo(() => WorkActivity)
  workActivityId: string;

  @belongsTo(() => IncidentCircumstanceCategory)
  incidentCircumstanceCategoryId: string;

  @belongsTo(() => IncidentCircumstanceDescription)
  incidentCircumstanceDescriptionId: string;

  @belongsTo(() => IncidentCircumstanceType)
  incidentCircumstanceTypeId: string;

  @belongsTo(() => User)
  userId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => User)
  investigatorId: string;

  @belongsTo(() => User)
  incidentOwnerId: string;

  locationOne: LocationOne;
  locationTwo: LocationTwo;
  locationThree: LocationThree;
  locationFour: LocationFour;
  locationFive: LocationFive;
  locationSix: LocationSix;
  workActivity: WorkActivity;
  riskCategory: RiskCategory;
  incidentCircumstanceType: IncidentCircumstanceType;
  incidentCircumstanceCategory: IncidentCircumstanceCategory;


  @hasMany(() => Action, {keyTo: 'objectId'})
  actions: Action[];

  constructor(data?: Partial<ReportIncident>) {
    super(data);
  }
}

export interface ReportIncidentRelations {
  // describe navigational properties here
}

export type ReportIncidentWithRelations = ReportIncident & ReportIncidentRelations;
