import { inject } from '@loopback/core';
import { get, param, Response, RestBindings } from '@loopback/rest';
import puppeteer from 'puppeteer';
import { PermitReport, EptwChecklist } from '../models';
import { FilterExcludingWhere, repository } from '@loopback/repository';
import { PermitReportRepository, EptwChecklistRepository } from '../repositories';
export class PdfController {

    constructor(
        @repository(PermitReportRepository)
        public permitReportRepository: PermitReportRepository,
        @repository(EptwChecklistRepository)
        public eptwChecklistRepository: EptwChecklistRepository,
    ) { }

    @get('/download-pdf/{id}', {
        responses: {
            '200': {
                description: 'Download PDF',
                content: {
                    'application/pdf': {
                        schema: { type: 'string', format: 'binary' },
                    },
                },
            },
        },
    })
    async downloadPdf(
        @inject(RestBindings.Http.RESPONSE) response: Response,
        @param.path.string('id') id: string,
        @param.filter(PermitReport, { exclude: 'where' }) filter?: FilterExcludingWhere<PermitReport>
    ): Promise<void> {

        try {

            const extendedFilter = {
                include: [
                    { relation: 'permitReportAction' },
                    { relation: 'applicant' },
                    { relation: 'locationOne' },
                    { relation: 'locationTwo' },
                    { relation: 'locationThree' },
                    { relation: 'locationFour' },
                    { relation: 'locationFive' },
                    { relation: 'locationSix' },
                ],
            };
            const permit = await this.permitReportRepository.findById(id, extendedFilter);
            const permitChecklist = await this.eptwChecklistRepository.find();
            const modifiedData = permitChecklist.map((item, index) => {
                const newItem = new EptwChecklist({
                    ...item,
                    id: index + 1  // Assigning sequential IDs starting from 1
                });
                return newItem;
            })
            const html = `
    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permit to Work - DC Operation</title>
    <link rel="stylesheet" href="styles.css">
    <script src="script.js" defer></script>
</head>
<style>
    @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&family=IBM+Plex+Mono:wght@400;500;600&display=swap');

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background-color: #f8fafc;
        color: #1e293b;
        line-height: 1.5;
        font-size: 14px;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0;
        background: #ffffff;
        min-height: 100vh;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    /* Header Styles */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        background: #ffffff;
        border-bottom: 1px solid #e2e8f0;
        position: sticky;
        top: 0;
        z-index: 100;
        backdrop-filter: blur(8px);
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .logo-section {
        display: flex;
        align-items: center;
    }

    .logo {
        background-color: #e91e63;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 14px;
        letter-spacing: 0.5px;
    }

    .header-left h1 {
        font-size: 20px;
        font-weight: 600;
        color: #0f172a;
        letter-spacing: -0.025em;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .status-section {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .status-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4px;
    }

    .status-label {
        font-size: 11px;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    .status-value {
        background: #fbbf24;
        color: #92400e;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 13px;
        font-weight: 500;
        border: 1px solid #f59e0b;
    }

    .status-timestamp {
        font-size: 10px;
        color: #64748b;
        font-style: italic;
        font-weight: 400;
    }

    .btn-download {
        background: #3b82f6;
        color: #ffffff;
        border: 1px solid #3b82f6;
        border-radius: 6px;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.15s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-download:hover {
        background: #2563eb;
        border-color: #2563eb;
    }

    /* Info Grid Container */
    .info-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin: 32px;
        gap: 32px;
    }

    /* Info Grid */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(2, minmax(240px, 1fr));
        gap: 1px;
        background: #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
        flex: 1;
        max-width: 600px;
    }

    .info-item {
        display: flex;
        flex-direction: column;
        gap: 4px;
        padding: 16px 20px;
        background: #ffffff;
        transition: background-color 0.15s ease;
    }

    .info-item:hover {
        background: #f8fafc;
    }

    .info-item label {
        font-weight: 500;
        color: #64748b;
        font-size: 11px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .info-item span {
        font-size: 14px;
        color: #0f172a;
        font-weight: 500;
        line-height: 1.4;
    }

    .status-closed {
        background: #dcfce7;
        color: #166534;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px !important;
        display: inline-block;
        width: fit-content;
        font-weight: 500;
        border: 1px solid #bbf7d0;
    }

    /* QR Code Section */
    .header-qr-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        min-width: 140px;
    }

    .header-qr-code {
        margin-bottom: 8px;
    }

    .qr-text {
        font-size: 10px;
        color: #64748b;
        text-align: center;
        margin-bottom: 12px;
        line-height: 1.4;
        max-width: 120px;
    }

    .header-qr-info {
        text-align: center;
    }

    .header-qr-label {
        font-size: 10px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 4px;
    }

    .header-qr-reference {
        font-size: 11px;
        font-weight: 600;
        color: #1e293b;
        font-family: 'IBM Plex Mono', monospace;
        word-break: break-all;
    }

    /* Section Styles */
    .section {
        margin: 0 32px 32px 32px;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
    }

    .section h2 {
        font-size: 16px;
        font-weight: 600;
        color: #0f172a;
        margin: 0;
        padding: 16px 24px;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
        letter-spacing: -0.025em;
    }

    /* Work Details */
    .work-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1px;
        background: #e2e8f0;
        margin: 0;
    }

    .detail-item {
        font-size: 13px;
        padding: 12px 16px;
        background: #ffffff;
        color: #475569;
        line-height: 1.4;
        word-break: break-word;
    }

    .detail-item strong {
        color: #1e293b;
        font-weight: 500;
        display: block;
        margin-bottom: 2px;
    }

    /* Location Details */
    .location-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1px;
        background: #e2e8f0;
        margin: 16px 0 0 0;
    }

    .location-item {
        font-size: 13px;
        color: #475569;
        padding: 12px 16px;
        background: #ffffff;
        line-height: 1.4;
    }

    .location-item strong {
        color: #1e293b;
        font-weight: 500;
    }

    .description {
        padding: 16px 24px;
        border-top: 1px solid #e2e8f0;
    }

    .description strong {
        color: #1e293b;
        font-weight: 500;
        font-size: 13px;
    }

    .description p {
        margin-top: 8px;
        color: #475569;
        line-height: 1.5;
        font-size: 13px;
    }

    /* Uploaded Documents */
    .uploaded-documents {
        padding: 16px 24px;
        border-top: 1px solid #e2e8f0;
    }

    .uploaded-documents strong {
        display: block;
        margin-bottom: 12px;
        font-size: 13px;
        color: #1e293b;
        font-weight: 500;
    }

    .documents-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
    }

    .document-item {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 12px;
        transition: all 0.15s ease;
    }

    .document-item:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
    }

    .document-preview {
        position: relative;
        margin-bottom: 12px;
    }

    .document-icon {
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 16px;
        z-index: 2;
    }

    .document-thumbnail {
        width: 100%;
        height: 80px;
        border-radius: 6px;
        overflow: hidden;
        border: 1px solid #e9ecef;
        background: #f8f9fa;
    }

    /* PDF Preview */
    .pdf-preview {
        width: 100%;
        height: 100%;
        background: white;
        padding: 8px;
        display: flex;
        flex-direction: column;
    }

    .pdf-header {
        font-size: 10px;
        font-weight: bold;
        color: #dc3545;
        margin-bottom: 6px;
    }

    .pdf-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .pdf-line {
        height: 2px;
        background: #dee2e6;
        border-radius: 1px;
    }

    .pdf-line.short {
        width: 60%;
    }

    .pdf-line.medium {
        width: 80%;
    }

    /* Image Preview */
    .image-preview {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px;
    }

    .image-icon {
        font-size: 20px;
        margin-bottom: 4px;
    }

    .image-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2px;
        width: 24px;
        height: 24px;
    }

    .grid-square {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 1px;
    }

    /* Excel Preview */
    .excel-preview {
        width: 100%;
        height: 100%;
        background: white;
        padding: 6px;
        display: flex;
        flex-direction: column;
    }

    .excel-header {
        font-size: 10px;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 4px;
    }

    .excel-grid {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1px;
    }

    .excel-row {
        display: flex;
        gap: 1px;
    }

    .excel-cell {
        flex: 1;
        height: 12px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        font-size: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #495057;
    }

    .excel-cell.header {
        background: #e9ecef;
        font-weight: bold;
    }

    /* Word Preview */
    .word-preview {
        width: 100%;
        height: 100%;
        background: white;
        padding: 8px;
        display: flex;
        flex-direction: column;
    }

    .word-header {
        font-size: 10px;
        font-weight: bold;
        color: #007bff;
        margin-bottom: 6px;
    }

    .word-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 3px;
    }

    .word-title {
        height: 4px;
        background: #495057;
        border-radius: 1px;
        width: 70%;
        margin-bottom: 2px;
    }

    .word-line {
        height: 2px;
        background: #dee2e6;
        border-radius: 1px;
    }

    .word-line.short {
        width: 60%;
    }

    .document-info {
        margin-bottom: 12px;
    }

    .document-name {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 4px;
        word-break: break-word;
    }

    .document-meta {
        font-size: 12px;
        color: #6c757d;
    }

    .document-actions {
        display: flex;
        gap: 8px;
    }

    .btn-view,
    .btn-download {
        flex: 1;
        padding: 6px 12px;
        border: 1px solid #e9ecef;
        background: white;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
    }

    .btn-view:hover {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .btn-download:hover {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    /* Risk Activities */
    .risk-activities {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        padding: 16px 24px;
    }

    .risk-tag {
        background: #fef3c7;
        color: #92400e;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid #fbbf24;
    }

    /* Fire Isolation Location */
    .fire-isolation-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
        padding: 16px 24px;
    }

    .isolation-location {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        overflow: hidden;
    }

    .location-header {
        background: #f8fafc;
        padding: 12px 16px;
        border-bottom: 1px solid #e2e8f0;
    }

    .location-header h3 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #1e293b;
    }

    .isolation-items {
        padding: 0;
    }

    .isolation-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f1f5f9;
        font-size: 13px;
    }

    .isolation-item:last-child {
        border-bottom: none;
    }

    .isolation-label {
        color: #475569;
        font-weight: 400;
    }

    .isolation-status {
        font-size: 11px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        text-align: center;
        min-width: 80px;
    }

    .isolation-status.not-isolated {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .isolation-status.isolated {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
    }

    /* Checklist Categories */
    .checklist-category {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 15px 20px;
        margin: 20px 0 10px 0;
        border-radius: 0 4px 4px 0;
    }

    .checklist-category h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
    }

    .checklist-category:first-child {
        margin-top: 0;
    }

    /* Checklist Items */
    .checklist-items {
        background: #ffffff;
        border-radius: 0;
        padding: 0;
        border: none;
        overflow: hidden;
    }

    .checklist-item {
        display: grid;
        grid-template-columns: 1fr auto;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f1f5f9;
        gap: 24px;
        min-height: 56px;
        transition: background-color 0.15s ease;
    }

    .checklist-item:last-child {
        border-bottom: none;
    }

    .checklist-item:hover {
        background: #f8fafc;
    }

    .item-text {
        font-size: 13px;
        color: #374151;
        line-height: 1.5;
        font-weight: 400;
        margin: 0;
    }

    .answer-group {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;
    }

    .answer-yes,
    .answer-no,
    .answer-na {
        font-size: 11px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-width: 50px;
        white-space: nowrap;
        border: 1px solid;
    }

    .answer-yes {
        background: #f0fdf4;
        color: #166534;
        border-color: #bbf7d0;
    }

    .answer-no {
        background: #fef2f2;
        color: #dc2626;
        border-color: #fecaca;
    }

    .answer-na {
        background: #f8fafc;
        color: #64748b;
        border-color: #e2e8f0;
    }

    .remarks {
        grid-column: 1 / -1;
        margin-top: 8px;
        padding: 12px 16px;
        background: #fef3c7;
        border-left: 3px solid #f59e0b;
        border-radius: 0 4px 4px 0;
        display: flex;
        align-items: flex-start;
        gap: 8px;
    }

    .remarks-icon {
        font-size: 14px;
        color: #d97706;
        margin-top: 1px;
    }

    .remarks-text {
        font-size: 12px;
        color: #92400e;
        line-height: 1.4;
        font-style: italic;
    }

    .inspector-info {
        grid-column: 1 / -1;
        margin-top: 8px;
        padding: 12px 16px;
        background: #ecfdf5;
        border-left: 3px solid #10b981;
        border-radius: 0 4px 4px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .inspector-info::before {
        content: "👤";
        font-size: 12px;
    }

    .inspector-text {
        font-size: 12px;
        color: #065f46;
        font-weight: 500;
    }

    /* Signatures */
    .signatures-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 16px;
        padding: 16px 24px;
    }

    .signature-card {
        background: #ffffff;
        padding: 16px;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        transition: all 0.15s ease;
    }

    .signature-card:hover {
        border-color: #cbd5e1;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .signature-card h3 {
        font-size: 14px;
        color: #1e293b;
        margin-bottom: 12px;
        font-weight: 600;
    }

    .declaration {
        background: #f8fafc;
        border-left: 3px solid #3b82f6;
        padding: 12px 16px;
        margin-bottom: 12px;
        border-radius: 0 4px 4px 0;
    }

    .declaration p {
        font-size: 12px;
        color: #64748b;
        line-height: 1.4;
        margin: 0;
        font-style: italic;
    }

    .signature-area {
        margin-bottom: 12px;
        padding: 12px;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 50px;
    }

    .signature-image {
        opacity: 0.8;
    }

    .signature-image svg {
        display: block;
        width: 100px;
        height: 30px;
    }

    .signature-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .signature-info strong {
        font-size: 13px;
        color: #1e293b;
        font-weight: 600;
    }

    .signature-date {
        font-size: 11px;
        color: #64748b;
        font-weight: 400;
    }

    .signature-status.completed {
        color: #16a34a;
        font-size: 11px;
        font-weight: 500;
    }

    /* Actions */
    .actions-list {
        margin-bottom: 20px;
    }

    .action-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .action-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .action-id {
        font-size: 12px;
        color: #6c757d;
    }

    .action-title {
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
    }

    .action-meta {
        display: flex;
        align-items: center;
        gap: 15px;
        font-size: 13px;
    }

    .action-date,
    .action-assignee {
        color: #6c757d;
    }

    .action-status.completed {
        color: #28a745;
        font-weight: 500;
    }

    .action-arrow {
        font-size: 18px;
        color: #6c757d;
    }

    .add-action {
        text-align: center;
    }

    .btn-add-action {
        padding: 12px 24px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .btn-add-action:hover {
        background: #0056b3;
    }

    /* Additional Styling */
    .checklist-item.has-remarks {
        grid-template-rows: auto auto;
    }

    .checklist-item.has-inspector {
        grid-template-rows: auto auto;
    }

    .btn-status:hover,
    .btn-progress:hover,
    .btn-export:hover {
        opacity: 0.9;
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .action-item:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.2s ease;
    }

    .signature-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.2s ease;
    }

    /* Status Indicators */
    .status-pending {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        color: #856404;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 14px !important;
        display: inline-block;
        width: fit-content;
        border: 1px solid #f1c40f;
        box-shadow: 0 2px 4px rgba(241, 196, 15, 0.2);
    }

    .status-in-progress {
        background: linear-gradient(135deg, #cce5ff, #74b9ff);
        color: #004085;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 14px !important;
        display: inline-block;
        width: fit-content;
        border: 1px solid #0984e3;
        box-shadow: 0 2px 4px rgba(9, 132, 227, 0.2);
    }

    /* Footer Styles */
    .footer {
        margin: 32px;
        padding: 24px 0;
        background: #f8fafc;
        border-top: 1px solid #e2e8f0;
        border-radius: 0;
    }

    .footer-content {
        display: grid;
        grid-template-columns: auto 1fr auto;
        gap: 24px;
        align-items: center;
        max-width: 100%;
        padding: 0 24px;
    }

    .footer-left {
        display: flex;
        align-items: center;
    }

    .qr-code-section {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
    }

    .qr-code {
        flex-shrink: 0;
    }

    .qr-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .qr-label {
        font-size: 10px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .qr-reference {
        font-size: 12px;
        font-weight: 600;
        color: #1e293b;
        font-family: 'IBM Plex Mono', monospace;
    }

    .footer-center {
        text-align: center;
        padding: 0 16px;
    }

    .document-info-footer h3 {
        margin: 0 0 8px 0;
        font-size: 16px;
        color: #1e293b;
        font-weight: 600;
    }

    .document-info-footer p {
        margin: 0;
        font-size: 12px;
        color: #64748b;
        line-height: 1.4;
        max-width: 350px;
        margin: 0 auto;
    }

    .footer-right {
        display: flex;
        align-items: center;
    }

    .timestamp-section {
        padding: 12px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        min-width: 200px;
    }

    .timestamp-label {
        font-size: 10px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 2px;
    }

    .timestamp-value {
        font-size: 11px;
        color: #1e293b;
        font-weight: 600;
        margin-bottom: 8px;
        font-family: 'IBM Plex Mono', monospace;
    }

    .validity-info {
        padding-top: 8px;
        border-top: 1px solid #e2e8f0;
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .validity-label {
        font-size: 10px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .validity-date {
        font-size: 11px;
        color: #dc2626;
        font-weight: 600;
        font-family: 'IBM Plex Mono', monospace;
    }

    .avoid-break {
      page-break-inside: avoid;
      break-inside: avoid;
    }

     .page-break {
      page-break-before: always;
    }
    /* Responsive Design */
    @media (max-width: 768px) {
        .container {
            margin: 0;
            border-radius: 0;
        }

        .header {
            flex-direction: column;
            gap: 12px;
            align-items: flex-start;
            padding: 16px 24px;
        }

        .header-right {
            width: 100%;
            justify-content: flex-start;
        }

        .info-container {
            flex-direction: column;
            margin: 16px;
            gap: 16px;
        }

        .info-grid {
            grid-template-columns: 1fr;
            max-width: none;
        }

        .header-qr-section {
            align-self: center;
            min-width: 120px;
        }

        .section {
            margin: 0 16px 16px 16px;
        }

        .checklist-item {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 12px 16px;
        }

        .answer-group {
            justify-self: start;
            margin-top: 4px;
        }

        .signatures-grid {
            grid-template-columns: 1fr;
            padding: 16px;
        }

        .documents-grid {
            grid-template-columns: 1fr;
        }

        .fire-isolation-grid {
            grid-template-columns: 1fr;
            padding: 16px;
        }

        /* Footer responsive */
        .footer {
            margin: 16px;
        }

        .footer-content {
            grid-template-columns: 1fr;
            gap: 16px;
            text-align: center;
            padding: 0 16px;
        }

        .qr-code-section {
            justify-content: center;
            margin: 0 auto;
            max-width: 250px;
        }

        .timestamp-section {
            margin: 0 auto;
            max-width: 250px;
        }

        .document-info-footer p {
            max-width: 100%;
        }
    }
</style>

<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="header-left">
                <div class="logo-section">
                    <div class="logo">LOGO</div>
                </div>
                <h1>DC Operation PERMIT TO WORK</h1>
            </div>
            <div class="header-right">
                <div class="status-section">
                    <div class="status-info">
                        <span class="status-label">Status</span>
                        <span class="status-value">In Progress</span>
                        <span class="status-timestamp" id="status-timestamp">As of 24/06/25, 14:30:45</span>
                    </div>
                    <button class="btn-download" title="Download">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                            <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Basic Information Container -->
        <div class="info-container">
            <div class="info-grid">
                <div class="info-item">
                    <label>Type</label>
                    <span>DC Operation</span>
                </div>
                <div class="info-item">
                    <label>Reference #</label>
                    <span>#250612-PTW-DC-4632</span>
                </div>
                <div class="info-item">
                    <label>Applied Date</label>
                    <span>12th Jun 2025</span>
                </div>
                <div class="info-item">
                    <label>Location</label>
                    <span>Singapore, Singapore (SG) - Ophir - Level 2 - carpark</span>
                </div>
                <div class="info-item">
                    <label>Applied by</label>
                    <span>Adhi Internal</span>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="header-qr-section">
                <div class="header-qr-code">
                    <svg width="100" height="100" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                        <!-- QR Code pattern based on the provided image -->
                        <rect width="25" height="25" fill="white" />

                        <!-- Top-left finder pattern -->
                        <rect x="0" y="0" width="7" height="7" fill="black" />
                        <rect x="1" y="1" width="5" height="5" fill="white" />
                        <rect x="2" y="2" width="3" height="3" fill="black" />

                        <!-- Top-right finder pattern -->
                        <rect x="18" y="0" width="7" height="7" fill="black" />
                        <rect x="19" y="1" width="5" height="5" fill="white" />
                        <rect x="20" y="2" width="3" height="3" fill="black" />

                        <!-- Bottom-left finder pattern -->
                        <rect x="0" y="18" width="7" height="7" fill="black" />
                        <rect x="1" y="19" width="5" height="5" fill="white" />
                        <rect x="2" y="20" width="3" height="3" fill="black" />

                        <!-- Timing patterns -->
                        <rect x="8" y="6" width="1" height="1" fill="black" />
                        <rect x="10" y="6" width="1" height="1" fill="black" />
                        <rect x="12" y="6" width="1" height="1" fill="black" />
                        <rect x="14" y="6" width="1" height="1" fill="black" />
                        <rect x="16" y="6" width="1" height="1" fill="black" />

                        <rect x="6" y="8" width="1" height="1" fill="black" />
                        <rect x="6" y="10" width="1" height="1" fill="black" />
                        <rect x="6" y="12" width="1" height="1" fill="black" />
                        <rect x="6" y="14" width="1" height="1" fill="black" />
                        <rect x="6" y="16" width="1" height="1" fill="black" />

                        <!-- Data modules (sample pattern) -->
                        <rect x="8" y="0" width="1" height="1" fill="black" />
                        <rect x="9" y="0" width="1" height="1" fill="black" />
                        <rect x="11" y="0" width="1" height="1" fill="black" />
                        <rect x="12" y="0" width="1" height="1" fill="black" />
                        <rect x="15" y="0" width="1" height="1" fill="black" />
                        <rect x="16" y="0" width="1" height="1" fill="black" />

                        <rect x="8" y="1" width="1" height="1" fill="black" />
                        <rect x="10" y="1" width="1" height="1" fill="black" />
                        <rect x="13" y="1" width="1" height="1" fill="black" />
                        <rect x="15" y="1" width="1" height="1" fill="black" />
                        <rect x="17" y="1" width="1" height="1" fill="black" />

                        <rect x="9" y="2" width="1" height="1" fill="black" />
                        <rect x="11" y="2" width="1" height="1" fill="black" />
                        <rect x="12" y="2" width="1" height="1" fill="black" />
                        <rect x="14" y="2" width="1" height="1" fill="black" />
                        <rect x="16" y="2" width="1" height="1" fill="black" />
                        <rect x="17" y="2" width="1" height="1" fill="black" />

                        <rect x="8" y="3" width="1" height="1" fill="black" />
                        <rect x="10" y="3" width="1" height="1" fill="black" />
                        <rect x="11" y="3" width="1" height="1" fill="black" />
                        <rect x="13" y="3" width="1" height="1" fill="black" />
                        <rect x="15" y="3" width="1" height="1" fill="black" />
                        <rect x="17" y="3" width="1" height="1" fill="black" />

                        <rect x="8" y="4" width="1" height="1" fill="black" />
                        <rect x="9" y="4" width="1" height="1" fill="black" />
                        <rect x="11" y="4" width="1" height="1" fill="black" />
                        <rect x="13" y="4" width="1" height="1" fill="black" />
                        <rect x="14" y="4" width="1" height="1" fill="black" />
                        <rect x="16" y="4" width="1" height="1" fill="black" />
                        <rect x="17" y="4" width="1" height="1" fill="black" />

                        <rect x="8" y="5" width="1" height="1" fill="black" />
                        <rect x="10" y="5" width="1" height="1" fill="black" />
                        <rect x="12" y="5" width="1" height="1" fill="black" />
                        <rect x="13" y="5" width="1" height="1" fill="black" />
                        <rect x="15" y="5" width="1" height="1" fill="black" />
                        <rect x="17" y="5" width="1" height="1" fill="black" />

                        <!-- More data pattern -->
                        <rect x="0" y="8" width="1" height="1" fill="black" />
                        <rect x="2" y="8" width="1" height="1" fill="black" />
                        <rect x="3" y="8" width="1" height="1" fill="black" />
                        <rect x="4" y="8" width="1" height="1" fill="black" />
                        <rect x="5" y="8" width="1" height="1" fill="black" />
                        <rect x="8" y="8" width="1" height="1" fill="black" />
                        <rect x="9" y="8" width="1" height="1" fill="black" />
                        <rect x="11" y="8" width="1" height="1" fill="black" />
                        <rect x="13" y="8" width="1" height="1" fill="black" />
                        <rect x="15" y="8" width="1" height="1" fill="black" />
                        <rect x="17" y="8" width="1" height="1" fill="black" />
                        <rect x="18" y="8" width="1" height="1" fill="black" />
                        <rect x="20" y="8" width="1" height="1" fill="black" />
                        <rect x="21" y="8" width="1" height="1" fill="black" />
                        <rect x="22" y="8" width="1" height="1" fill="black" />
                        <rect x="24" y="8" width="1" height="1" fill="black" />

                        <!-- Continue with more complex pattern -->
                        <rect x="0" y="9" width="1" height="1" fill="black" />
                        <rect x="1" y="9" width="1" height="1" fill="black" />
                        <rect x="3" y="9" width="1" height="1" fill="black" />
                        <rect x="5" y="9" width="1" height="1" fill="black" />
                        <rect x="8" y="9" width="1" height="1" fill="black" />
                        <rect x="10" y="9" width="1" height="1" fill="black" />
                        <rect x="12" y="9" width="1" height="1" fill="black" />
                        <rect x="14" y="9" width="1" height="1" fill="black" />
                        <rect x="16" y="9" width="1" height="1" fill="black" />
                        <rect x="18" y="9" width="1" height="1" fill="black" />
                        <rect x="19" y="9" width="1" height="1" fill="black" />
                        <rect x="21" y="9" width="1" height="1" fill="black" />
                        <rect x="23" y="9" width="1" height="1" fill="black" />

                        <!-- Additional data modules for realistic appearance -->
                        <rect x="1" y="10" width="1" height="1" fill="black" />
                        <rect x="2" y="10" width="1" height="1" fill="black" />
                        <rect x="4" y="10" width="1" height="1" fill="black" />
                        <rect x="8" y="10" width="1" height="1" fill="black" />
                        <rect x="9" y="10" width="1" height="1" fill="black" />
                        <rect x="11" y="10" width="1" height="1" fill="black" />
                        <rect x="13" y="10" width="1" height="1" fill="black" />
                        <rect x="15" y="10" width="1" height="1" fill="black" />
                        <rect x="17" y="10" width="1" height="1" fill="black" />
                        <rect x="19" y="10" width="1" height="1" fill="black" />
                        <rect x="20" y="10" width="1" height="1" fill="black" />
                        <rect x="22" y="10" width="1" height="1" fill="black" />
                        <rect x="24" y="10" width="1" height="1" fill="black" />

                        <!-- Continue pattern for rows 11-17 -->
                        <rect x="0" y="11" width="1" height="1" fill="black" />
                        <rect x="2" y="11" width="1" height="1" fill="black" />
                        <rect x="4" y="11" width="1" height="1" fill="black" />
                        <rect x="5" y="11" width="1" height="1" fill="black" />
                        <rect x="8" y="11" width="1" height="1" fill="black" />
                        <rect x="10" y="11" width="1" height="1" fill="black" />
                        <rect x="12" y="11" width="1" height="1" fill="black" />
                        <rect x="14" y="11" width="1" height="1" fill="black" />
                        <rect x="16" y="11" width="1" height="1" fill="black" />
                        <rect x="18" y="11" width="1" height="1" fill="black" />
                        <rect x="20" y="11" width="1" height="1" fill="black" />
                        <rect x="22" y="11" width="1" height="1" fill="black" />
                        <rect x="23" y="11" width="1" height="1" fill="black" />

                        <!-- Bottom section data -->
                        <rect x="8" y="18" width="1" height="1" fill="black" />
                        <rect x="9" y="18" width="1" height="1" fill="black" />
                        <rect x="11" y="18" width="1" height="1" fill="black" />
                        <rect x="13" y="18" width="1" height="1" fill="black" />
                        <rect x="15" y="18" width="1" height="1" fill="black" />
                        <rect x="17" y="18" width="1" height="1" fill="black" />
                        <rect x="18" y="18" width="1" height="1" fill="black" />
                        <rect x="20" y="18" width="1" height="1" fill="black" />
                        <rect x="22" y="18" width="1" height="1" fill="black" />
                        <rect x="24" y="18" width="1" height="1" fill="black" />

                        <rect x="8" y="19" width="1" height="1" fill="black" />
                        <rect x="10" y="19" width="1" height="1" fill="black" />
                        <rect x="12" y="19" width="1" height="1" fill="black" />
                        <rect x="14" y="19" width="1" height="1" fill="black" />
                        <rect x="16" y="19" width="1" height="1" fill="black" />
                        <rect x="18" y="19" width="1" height="1" fill="black" />
                        <rect x="19" y="19" width="1" height="1" fill="black" />
                        <rect x="21" y="19" width="1" height="1" fill="black" />
                        <rect x="23" y="19" width="1" height="1" fill="black" />

                        <rect x="8" y="20" width="1" height="1" fill="black" />
                        <rect x="9" y="20" width="1" height="1" fill="black" />
                        <rect x="11" y="20" width="1" height="1" fill="black" />
                        <rect x="13" y="20" width="1" height="1" fill="black" />
                        <rect x="15" y="20" width="1" height="1" fill="black" />
                        <rect x="17" y="20" width="1" height="1" fill="black" />
                        <rect x="19" y="20" width="1" height="1" fill="black" />
                        <rect x="21" y="20" width="1" height="1" fill="black" />
                        <rect x="22" y="20" width="1" height="1" fill="black" />
                        <rect x="24" y="20" width="1" height="1" fill="black" />

                        <rect x="8" y="21" width="1" height="1" fill="black" />
                        <rect x="10" y="21" width="1" height="1" fill="black" />
                        <rect x="12" y="21" width="1" height="1" fill="black" />
                        <rect x="14" y="21" width="1" height="1" fill="black" />
                        <rect x="16" y="21" width="1" height="1" fill="black" />
                        <rect x="18" y="21" width="1" height="1" fill="black" />
                        <rect x="20" y="21" width="1" height="1" fill="black" />
                        <rect x="22" y="21" width="1" height="1" fill="black" />
                        <rect x="23" y="21" width="1" height="1" fill="black" />

                        <rect x="8" y="22" width="1" height="1" fill="black" />
                        <rect x="9" y="22" width="1" height="1" fill="black" />
                        <rect x="11" y="22" width="1" height="1" fill="black" />
                        <rect x="13" y="22" width="1" height="1" fill="black" />
                        <rect x="15" y="22" width="1" height="1" fill="black" />
                        <rect x="17" y="22" width="1" height="1" fill="black" />
                        <rect x="19" y="22" width="1" height="1" fill="black" />
                        <rect x="21" y="22" width="1" height="1" fill="black" />
                        <rect x="23" y="22" width="1" height="1" fill="black" />
                        <rect x="24" y="22" width="1" height="1" fill="black" />

                        <rect x="8" y="23" width="1" height="1" fill="black" />
                        <rect x="10" y="23" width="1" height="1" fill="black" />
                        <rect x="12" y="23" width="1" height="1" fill="black" />
                        <rect x="14" y="23" width="1" height="1" fill="black" />
                        <rect x="16" y="23" width="1" height="1" fill="black" />
                        <rect x="18" y="23" width="1" height="1" fill="black" />
                        <rect x="20" y="23" width="1" height="1" fill="black" />
                        <rect x="22" y="23" width="1" height="1" fill="black" />
                        <rect x="24" y="23" width="1" height="1" fill="black" />

                        <rect x="8" y="24" width="1" height="1" fill="black" />
                        <rect x="9" y="24" width="1" height="1" fill="black" />
                        <rect x="11" y="24" width="1" height="1" fill="black" />
                        <rect x="13" y="24" width="1" height="1" fill="black" />
                        <rect x="15" y="24" width="1" height="1" fill="black" />
                        <rect x="17" y="24" width="1" height="1" fill="black" />
                        <rect x="19" y="24" width="1" height="1" fill="black" />
                        <rect x="21" y="24" width="1" height="1" fill="black" />
                        <rect x="23" y="24" width="1" height="1" fill="black" />
                    </svg>
                </div>
                <div class="qr-text">Scan QR code to view current status of the permit.</div>
                <div class="header-qr-info">
                    <div class="header-qr-label">Permit Reference</div>
                    <div class="header-qr-reference">#250612-PTW-DC-4632</div>
                </div>
            </div>
        </div>

        <!-- Work Details Section -->
        <div class="section">
            <h2>Work Details</h2>
            <div class="work-details">
                <div class="detail-item">
                    <strong>Work Start:</strong> 12th Jun 2025 11:50:00 PM
                </div>
                <div class="detail-item">
                    <strong>Work End:</strong> 13th Jun 2025 09:00:00 AM
                </div>
                <div class="detail-item">
                    <strong>Business Unit:</strong> Fitouts
                </div>
                <div class="detail-item">
                    <strong>Project/DC name:</strong> Ophir
                </div>
                <div class="detail-item">
                    <strong>Reference MOP Title:</strong> DC Maintenance Operations Procedure v2.1
                </div>
                <div class="detail-item">
                    <strong>Change Ticket No:</strong> CHG-2025-06-001234
                </div>
                <div class="detail-item">
                    <strong>Applicant Contact Number:</strong> +65 9123 4567
                </div>
                <div class="detail-item">
                    <strong>Company Contact Number:</strong> +65 6789 0123
                </div>
                <div class="detail-item">
                    <strong>Level:</strong> Level 2
                </div>
                <div class="detail-item">
                    <strong>Zone:</strong> Carpark
                </div>
            </div>

            <div class="description">
                <strong>Description:</strong>
                <p>Lorem ipsum is a placeholder or dummy text used in typesetting and graphic design for previewing
                    layouts. It features scrambled Latin text, which emphasizes the design over content of the layout.
                </p>
            </div>

            <div class="uploaded-documents avoid-break">
                <strong>Uploaded Documents:</strong>
                <div class="documents-grid">
                    <div class="document-item">
                        <div class="document-preview">
                            <div class="document-icon pdf">📄</div>
                            <div class="document-thumbnail">
                                <div class="pdf-preview">
                                    <div class="pdf-header">PDF</div>
                                    <div class="pdf-content">
                                        <div class="pdf-line"></div>
                                        <div class="pdf-line short"></div>
                                        <div class="pdf-line"></div>
                                        <div class="pdf-line medium"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="document-info">
                            <span class="document-name">Safety_Work_Method_Statement.pdf</span>
                            <span class="document-meta">2.4 MB </span>
                        </div>
                        <!-- <div class="document-actions">
                            <button class="btn-view">👁️ View</button>
                            <button class="btn-download">⬇️ Download</button>
                        </div> -->
                    </div>

                    <div class="document-item">
                        <div class="document-preview">
                            <div class="document-icon image">🖼️</div>
                            <div class="document-thumbnail">
                                <div class="image-preview">
                                    <div class="image-placeholder">
                                        <div class="image-icon">📷</div>
                                        <div class="image-grid">
                                            <div class="grid-square"></div>
                                            <div class="grid-square"></div>
                                            <div class="grid-square"></div>
                                            <div class="grid-square"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="document-info">
                            <span class="document-name">Site_Layout_Plan.jpg</span>
                            <span class="document-meta">1.8 MB </span>
                        </div>
                        <!-- <div class="document-actions">
                            <button class="btn-view">👁️ View</button>
                            <button class="btn-download">⬇️ Download</button>
                        </div> -->
                    </div>



                    <div class="document-item">
                        <div class="document-preview">
                            <div class="document-icon word">📝</div>
                            <div class="document-thumbnail">
                                <div class="word-preview">
                                    <div class="word-header">DOC</div>
                                    <div class="word-content">
                                        <div class="word-title"></div>
                                        <div class="word-line"></div>
                                        <div class="word-line short"></div>
                                        <div class="word-line"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="document-info">
                            <span class="document-name">Risk_Assessment_Report.docx</span>
                            <span class="document-meta">1.2 MB F</span>
                        </div>
                        <!-- <div class="document-actions">
                            <button class="btn-view">👁️ View</button>
                            <button class="btn-download">⬇️ Download</button>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Fire Isolation Location(s) -->
        <div class="section">
            <h2>Fire Isolation Location(s)</h2>
            <div class="fire-isolation-grid">
                <div class="isolation-location">
                    <div class="location-header">
                        <h3>Level 3 - External Façade</h3>
                    </div>
                    <div class="isolation-items">
                        <div class="isolation-item">
                            <span class="isolation-label">Is the Vesda Isolated?</span>
                            <span class="isolation-status not-isolated">Not isolated yet</span>
                        </div>
                        <div class="isolation-item">
                            <span class="isolation-label">Is the Smoke Detector Isolated?</span>
                            <span class="isolation-status not-isolated">Not isolated yet</span>
                        </div>
                    </div>
                </div>

                <div class="isolation-location">
                    <div class="location-header">
                        <h3>Level 5 - External Façade</h3>
                    </div>
                    <div class="isolation-items">
                        <div class="isolation-item">
                            <span class="isolation-label">Is the Vesda Isolated?</span>
                            <span class="isolation-status not-isolated">Not isolated yet</span>
                        </div>
                        <div class="isolation-item">
                            <span class="isolation-label">Is the Smoke Detector Isolated?</span>
                            <span class="isolation-status not-isolated">Not isolated yet</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- High Risk Activities -->
        <div class="section">
            <h2>High Risk Activities</h2>
            <div class="risk-activities">
                <span class="risk-tag">Work at Height</span>
                <span class="risk-tag">Confined Space</span>
                <span class="risk-tag">Piling Works</span>
            </div>
        </div>
    <div class="page-break"></div>
        <!-- Safety Checklist -->
        <div class="section">
            <h2>Safety Checklist</h2>
            <div class="checklist-items">

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 1: Endorsed SWMS/SWP is in place and all workers are inducted into it. All
                        SWMS/SWP requirements are compliant</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 2: Weather conditions are suitable for this task to proceed</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break has-inspector">
                    <span class="item-text"> 3: PPE that are required by SWMS/SWP have been inspected to be fit for
                        purpose</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                    <div class="inspector-info">
                        <span class="inspector-text">JC Sekar</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 4: Qualification & competence of all plant operators & trade persons have
                        been verified</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 5: Safe means of access/egress (ZERO fall risk) is provided to/from all
                        work areas</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 6: A suitable exclusion zone is in place that prevents unauthorised
                        access</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 7: Adequate lighting is provided (including emergency lighting)</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 8: Adequate ventilation (forced and/or natural) to prevent hazardous
                        atmosphere is present</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>



                <div class="checklist-item avoid-break">
                    <span class="item-text"> 9: Qualification & competence of all workers, supervisors, assessors and
                        managers for this high-risk activity have been verified</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 10: Risk of falling material is adequately controlled in accordance with
                        Hierarchy of Risk Control</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 11: Fall prevention equipment have appropriate maintenance and inspection
                        records, and are in good working condition</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break has-inspector">
                    <span class="item-text"> 12: Harness anchor point(s) are correctly rated, signed off & visually
                        inspected by a competent person</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                    <div class="inspector-info">
                        <span class="inspector-text">Surendhar</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 13: SWMS/SWP incorporates an adequate rescue plan, and all requirements are
                        confirmed to be in place</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>



                <div class="checklist-item avoid-break">
                    <span class="item-text"> 14: Adequate means of fall prevention is in place; harness required is NOT
                        as primary fall protection</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 15: Critical areas have been identified. Hazards & services have been
                        isolated (LOTO) or made safe</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 16: Isolation of services has been tested and confirmed</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 17: Confined space operation and rescue equipment have appropriate
                        maintenance, inspection and calibration records</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 18: Confined space access/egress is in safe condition and suitably
                        controlled</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break has-remarks">
                    <span class="item-text"> 19: Air Quality Tester (inspected and in good operating condition) is
                        available</span>
                    <div class="answer-group">
                        <span class="answer-no">✗ No</span>
                    </div>
                    <div class="remarks">
                        <span class="remarks-icon">💬</span>
                        <span class="remarks-text">Lorem ipsum is a placeholder or dummy text used in typesetting and
                            graphic design for previewing layouts.</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break has-remarks">
                    <span class="item-text"> 20: Confined space has been checked for atmospheric gas and verified 'Safe
                        for Entry'</span>
                    <div class="answer-group">
                        <span class="answer-na">N/A</span>
                    </div>
                    <div class="remarks">
                        <span class="remarks-icon">💬</span>
                        <span class="remarks-text">Not applicable for this work area</span>
                    </div>
                </div>




                <div class="checklist-item avoid-break">
                    <span class="item-text"> 21: Design has been verified by competent person (including structural
                        ties)</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 22: Detection/locating has taken place to identify any hidden existing
                        services</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 23: SWMS/SWP incorporates an acceptable procedure to manually expose & mark
                        existing underground services</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 24: Adjoining buildings/structures have been reviewed and any identified
                        risk has been adequately controlled</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>

                <div class="checklist-item avoid-break">
                    <span class="item-text"> 25: Suitable plans to control water/wet slurry, noise and/or dust have been
                        established</span>
                    <div class="answer-group">
                        <span class="answer-yes">✓ Yes</span>
                    </div>
                </div>


            </div>

            <div class="page-break"></div>
            <!-- Signatures Section -->
            <div class="section">
                <h2>Signatures & Approvals</h2>
                <div class="signatures-grid">
                    <div class="signature-card avoid-break">
                        <h3>Applicant</h3>
                        <div class="declaration">
                            <p>I declare that I have read and understood the requirements of this permit. I confirm that
                                all information provided is accurate and complete. I will ensure that all work is
                                carried out in accordance with the approved SWMS/SWP and safety requirements.</p>
                        </div>
                        <div class="signature-area">
                            <div class="signature-image">
                                <svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 25 Q20 15, 30 25 T50 25 Q60 20, 70 25 T90 25 Q100 20, 110 25"
                                        stroke="#2c3e50" stroke-width="2" fill="none" stroke-linecap="round" />
                                    <path d="M15 30 Q25 20, 35 30 T55 30" stroke="#2c3e50" stroke-width="1.5"
                                        fill="none" stroke-linecap="round" />
                                </svg>
                            </div>
                        </div>
                        <div class="signature-info">
                            <strong>Adhi Internal</strong>
                            <span class="signature-date">Signed on: 12-06-2025 10:40 AM</span>
                            <span class="signature-status completed">✓ Completed</span>
                        </div>
                    </div>

                    <div class="signature-card avoid-break">
                        <h3>Assessor</h3>
                        <div class="declaration">
                            <p>I have assessed the proposed work activities and confirm that all safety requirements
                                have been identified and addressed. The risk assessment is adequate and appropriate
                                control measures are in place. I authorize the commencement of work under this permit.
                            </p>
                        </div>
                        <div class="signature-area">
                            <div class="signature-image">
                                <svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 20 Q25 10, 40 20 T70 20 Q85 15, 100 20 T110 20" stroke="#2c3e50"
                                        stroke-width="2" fill="none" stroke-linecap="round" />
                                    <path d="M20 28 Q35 18, 50 28 T80 28" stroke="#2c3e50" stroke-width="1.5"
                                        fill="none" stroke-linecap="round" />
                                    <circle cx="95" cy="25" r="2" fill="#2c3e50" />
                                </svg>
                            </div>
                        </div>
                        <div class="signature-info">
                            <strong>Surendhar</strong>
                            <span class="signature-date">Signed on: 12-06-2025 10:44 AM</span>
                            <span class="signature-status completed">✓ Completed</span>
                        </div>
                    </div>

                    <div class="signature-card avoid-break">
                        <h3>Approver</h3>
                        <div class="declaration">
                            <p>I approve this permit to work based on the assessment provided. I confirm that all
                                necessary safety measures and precautions have been considered and implemented. The work
                                may proceed as outlined in this permit subject to all conditions and requirements.</p>
                        </div>
                        <div class="signature-area">
                            <div class="signature-image">
                                <svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 25 Q30 12, 45 25 T75 25 Q90 18, 105 25" stroke="#2c3e50"
                                        stroke-width="2" fill="none" stroke-linecap="round" />
                                    <path d="M10 32 Q25 22, 40 32 T70 32 Q85 27, 100 32" stroke="#2c3e50"
                                        stroke-width="1.5" fill="none" stroke-linecap="round" />
                                    <path d="M55 15 L65 20 L55 25" stroke="#2c3e50" stroke-width="1.5" fill="none"
                                        stroke-linecap="round" />
                                </svg>
                            </div>
                        </div>
                        <div class="signature-info">
                            <strong>Venkatesh (Acuizen)</strong>
                            <span class="signature-date">Signed on: 12-06-2025 10:48 AM</span>
                            <span class="signature-status completed">✓ Completed</span>
                        </div>
                    </div>

                    <div class="signature-card avoid-break">
                        <h3>DCSO Representative</h3>
                        <div class="declaration">
                            <p>As the Data Center Safety Officer representative, I confirm that all DC-specific safety
                                protocols and procedures have been reviewed and are compliant. The work activities align
                                with data center operational requirements and safety standards.</p>
                        </div>
                        <div class="signature-area">
                            <div class="signature-image">
                                <svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 22 Q27 8, 42 22 T72 22 Q87 15, 102 22" stroke="#2c3e50"
                                        stroke-width="2" fill="none" stroke-linecap="round" />
                                    <path d="M18 30 Q33 20, 48 30 T78 30" stroke="#2c3e50" stroke-width="1.5"
                                        fill="none" stroke-linecap="round" />
                                    <path d="M85 18 Q95 25, 105 18" stroke="#2c3e50" stroke-width="1.5" fill="none"
                                        stroke-linecap="round" />
                                </svg>
                            </div>
                        </div>
                        <div class="signature-info">
                            <strong>Adhi Internal</strong>
                            <span class="signature-date">Signed on: 12-06-2025 10:54 AM</span>
                            <span class="signature-status completed">✓ Completed</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer with QR Code and Timestamp -->

        </div>
</body>

</html>
    
    `;

            const browser = await puppeteer.launch({
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox'],
            });

            const page = await browser.newPage();
            await page.setContent(html, { waitUntil: 'networkidle0' });
            const pdfBuffer = await page.pdf({ format: 'A4' });

            await browser.close();

            response.setHeader('Content-Type', 'application/pdf');
            response.setHeader('Content-Disposition', 'attachment; filename="generated.pdf"');
            response.setHeader('Content-Length', pdfBuffer.length);
            response.end(pdfBuffer); // IMPORTANT: Use .end() instead of .send()
        }
        catch (error) {
            console.error('Error generating PDF:', error);
            response.status(500).send('Error generating PDF');
        }
    }
}
