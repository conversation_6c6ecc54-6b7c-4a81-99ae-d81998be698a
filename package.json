{"name": "api", "version": "0.0.1", "description": "API", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "14 || 16 || 17 || 18"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t API .", "docker:run": "docker run -p 3000:3000 -d API", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": " <<EMAIL>>", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@loopback/authentication": "^9.0.8", "@loopback/authentication-jwt": "^0.12.8", "@loopback/authorization": "^0.12.8", "@loopback/boot": "^5.0.6", "@loopback/core": "^4.0.6", "@loopback/repository": "^5.1.2", "@loopback/rest": "^12.0.6", "@loopback/rest-explorer": "^5.0.6", "@loopback/service-proxy": "^5.0.6", "@microsoft/microsoft-graph-client": "^3.0.7", "aws-jwt-verify": "^4.0.0", "aws-sdk": "^2.1389.0", "axios": "^1.3.5", "dotenv": "^16.0.3", "google-auth-library": "^9.1.0", "googleapis": "^126.0.1", "isemail": "^3.2.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.0.1", "kv-redis": "^0.1.3", "loopback-connector-mongodb": "^5.6.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.1", "pdfmake": "^0.2.10", "puppeteer": "^24.10.2", "sharp": "^0.33.4", "tslib": "^2.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@loopback/build": "^9.0.6", "@loopback/eslint-config": "^13.0.6", "@loopback/testlab": "^5.0.6", "@types/multer": "^1.4.7", "@types/node": "^14.18.63", "@types/nodemailer": "^6.4.7", "@types/uuid": "^9.0.0", "eslint": "^8.28.0", "source-map-support": "^0.5.21", "typescript": "~4.9.4"}}