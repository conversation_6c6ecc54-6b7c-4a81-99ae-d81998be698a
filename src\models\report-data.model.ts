import { Entity, model, property, belongsTo} from '@loopback/repository';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';
import {User} from './user.model';

@model()
export class ReportData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  date?: string;

  @property({
    type: 'number',
  })
  numberOfEmployees?: number;

  @property({
    type: 'number',
  })
  dailyHoursOfEmployee?: number;

  @property({
    type: 'number',
  })
  workingDaysOfEmployee?: number;

  @property({
    type: 'number',
  })
  numberofContractors?: number;

  @property({
    type: 'number',
  })
  dailyHoursOfContractors?: number;

  @property({
    type: 'number',
  })
  workingDaysOfContractors?: number;

  @property({
    type: 'string',
  })
  yearAndMonth?: string;

  @property({
    type: 'number',
  })
  noOfSafety?: number;

  @property({
    type: 'number',
  })
  noOfToolbox?: number;

  @property({
    type: 'number',
  })
  noOfEhsTraining?: number;

  @property({
    type: 'number',
  })
  noOfInspection?: number;

  @property({
    type: 'number',
  })
  noOfManagmentSiteWalk?: number;

  @property({
    type: 'number',
  })
  noOfAuthority?: number;

  @property({
    type: 'number',
  })
  noOfSafeObservation?: number;

  @property({
    type: 'number',
  })
  noOfRiskObservation?: number;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  reviewedDate?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => User)
  userId: string;

  @belongsTo(() => User)
  reviewerId: string;

  constructor(data?: Partial<ReportData>) {
    super(data);
  }
}

export interface ReportDataRelations {
  // describe navigational properties here
}

export type ReportDataWithRelations = ReportData & ReportDataRelations;
